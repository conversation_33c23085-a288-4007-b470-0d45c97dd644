#!/usr/bin/env python3
"""
批量修复BibTeX文件中的chktex问题
"""

import re
import sys

def fix_bibtex_issues(content):
    """修复BibTeX文件中的真正需要修复的问题"""

    # 修复Warning 7: 重音符号问题
    # 将 {\'i} 替换为 {\'\i}
    content = re.sub(r"{\\'i}", r"{\\'\\i}", content)
    content = re.sub(r"{\\'I}", r"{\\'\\i}", content)  # 注意：大写I也用\i

    # 修复Warning 12: 词间距问题
    # 在某些特殊字符后添加适当的间距
    content = re.sub(r'({\\"[oO]})\s+([A-Z])', r'\1\\ \2', content)

    return content

def create_chktex_config():
    """创建chktex配置文件来抑制误报警告"""
    config_content = """# ChkTeX配置文件 - 抑制BibTeX文件中的常见误报警告

# 抑制Warning 8: 短横线长度警告
# 在BibTeX的ISBN、ISSN、DOI等字段中，短横线是正确的格式
CmdLine { -n8 }
"""

    with open('.chktexrc', 'w', encoding='utf-8') as f:
        f.write(config_content)
    print("已创建 .chktexrc 配置文件来抑制误报警告")

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_bibtex.py <bibtex_file> [--config-only]")
        print("  --config-only: 只创建配置文件，不修复文件")
        sys.exit(1)

    if "--config-only" in sys.argv:
        create_chktex_config()
        return

    filename = sys.argv[1]

    try:
        # 创建配置文件
        create_chktex_config()

        # 读取文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()

        # 修复问题
        fixed_content = fix_bibtex_issues(content)

        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(fixed_content)

        print(f"已修复 {filename} 中的BibTeX问题")
        print("运行 'chktex references.bib' 来检查剩余问题")

    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
